export const convertDateToISOWithTimezone = (dateString: string | undefined): string | undefined => {
	if (!dateString) return undefined;

	try {
		let year: number;
		let month: number;
		let day: number;

		if (dateString.includes("-") && dateString.length === 10) {
			const [yearStr, monthStr, dayStr] = dateString.split("-");
			year = Number(yearStr);
			month = Number(monthStr);
			day = Number(dayStr);
		} else if (dateString.includes("/")) {
			const [dayStr, monthStr, yearStr] = dateString.split("/");
			year = Number(yearStr);
			month = Number(monthStr);
			day = Number(dayStr);
		} else {
			throw new Error("Formato de data inválido");
		}

		// Valida se os valores são números válidos
		if (isNaN(year) || isNaN(month) || isNaN(day)) {
			throw new Error("Data contém valores inválidos");
		}

		// Valida se a data é válida
		if (month < 1 || month > 12 || day < 1 || day > 31) {
			throw new Error("Data fora do intervalo válido");
		}

		// Cria a data no formato ISO com timezone brasileiro (-03:00)
		const isoDate = `${year.toString().padStart(4, "0")}-${month.toString().padStart(2, "0")}-${day.toString().padStart(2, "0")}T09:00:00-03:00`;

		// Valida se a data criada é válida
		const testDate = new Date(isoDate);
		if (isNaN(testDate.getTime())) {
			throw new Error("Data resultante inválida");
		}

		return isoDate;
	} catch (error) {
		console.error("Erro ao converter data:", error, "Data original:", dateString);
		return undefined;
	}
};

/**
 * Converte uma data ISO string para o formato brasileiro dd/MM/yyyy
 * @param isoString - Data no formato ISO
 * @returns String no formato dd/MM/yyyy ou undefined se a data for inválida
 */
export const convertISOToBrazilianDate = (isoString: string | undefined): string | undefined => {
	if (!isoString) return undefined;

	try {
		const date = new Date(isoString);
		if (isNaN(date.getTime())) {
			throw new Error("Data ISO inválida");
		}

		const day = date.getDate().toString().padStart(2, "0");
		const month = (date.getMonth() + 1).toString().padStart(2, "0");
		const year = date.getFullYear().toString();

		return `${day}/${month}/${year}`;
	} catch (error) {
		console.error("Erro ao converter data ISO:", error, "Data original:", isoString);
		return undefined;
	}
};

/**
 * Converte uma data ISO string para o formato yyyy-MM-dd
 * @param isoString - Data no formato ISO
 * @returns String no formato yyyy-MM-dd ou undefined se a data for inválida
 */
export const convertISOToYearMonthDay = (isoString: string | undefined): string | undefined => {
	if (!isoString) return undefined;

	try {
		const date = new Date(isoString);
		if (isNaN(date.getTime())) {
			throw new Error("Data ISO inválida");
		}

		const year = date.getFullYear().toString();
		const month = (date.getMonth() + 1).toString().padStart(2, "0");
		const day = date.getDate().toString().padStart(2, "0");

		return `${year}-${month}-${day}`;
	} catch (error) {
		console.error("Erro ao converter data ISO:", error, "Data original:", isoString);
		return undefined;
	}
};
